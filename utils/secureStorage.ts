import * as SecureStore from 'expo-secure-store';
import { Platform } from 'react-native';

export class SecureStorage {
  private static instance: SecureStorage;
  
  private constructor() {}
  
  public static getInstance(): SecureStorage {
    if (!SecureStorage.instance) {
      SecureStorage.instance = new SecureStorage();
    }
    return SecureStorage.instance;
  }

  /**
   * Store data securely
   */
  async setItem(key: string, value: any): Promise<void> {
    try {
      const jsonValue = JSON.stringify(value);
      
      // Use SecureStore for native platforms, localStorage for web
      if (Platform.OS === 'web') {
        if (typeof window !== 'undefined' && window.localStorage) {
          window.localStorage.setItem(key, jsonValue);
        } else {
          throw new Error('localStorage not available');
        }
      } else {
        await SecureStore.setItemAsync(key, jsonValue);
      }
    } catch (error) {
      console.error('SecureStorage: Error setting item:', error);
      throw new Error('Failed to store data securely');
    }
  }

  /**
   * Retrieve data securely
   */
  async getItem<T>(key: string): Promise<T | null> {
    try {
      let value: string | null = null;
      
      // Use SecureStore for native platforms, localStorage for web
      if (Platform.OS === 'web') {
        if (typeof window !== 'undefined' && window.localStorage) {
          value = window.localStorage.getItem(key);
        } else {
          return null;
        }
      } else {
        value = await SecureStore.getItemAsync(key);
      }
      
      if (value === null) {
        return null;
      }
      return JSON.parse(value);
    } catch (error) {
      console.error('SecureStorage: Error getting item:', error);
      return null;
    }
  }

  /**
   * Remove data securely
   */
  async removeItem(key: string): Promise<void> {
    try {
      // Use SecureStore for native platforms, localStorage for web
      if (Platform.OS === 'web') {
        if (typeof window !== 'undefined' && window.localStorage) {
          window.localStorage.removeItem(key);
        }
      } else {
        await SecureStore.deleteItemAsync(key);
      }
    } catch (error) {
      console.error('SecureStorage: Error removing item:', error);
      throw new Error('Failed to remove data securely');
    }
  }

  /**
   * Check if SecureStore is available
   */
  async isAvailable(): Promise<boolean> {
    try {
      if (Platform.OS === 'web') {
        return typeof window !== 'undefined' && !!window.localStorage;
      } else {
        await SecureStore.setItemAsync('test', 'test');
        await SecureStore.deleteItemAsync('test');
        return true;
      }
    } catch {
      return false;
    }
  }
}

// Export singleton instance
export const secureStorage = SecureStorage.getInstance();
