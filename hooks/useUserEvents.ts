import { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase/client';
import { useAuth } from '../contexts/AuthContext';
import { logger } from '../utils/logger';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface UserEvent {
  id: string;
  user_id: string;
  event_name: string;
  created_at: string;
}

export interface UseUserEventsReturn {
  logEvent: (eventName: string) => Promise<boolean>;
  hasEvent: (eventName: string) => boolean;
  getUserEvents: () => UserEvent[];
  isLoading: boolean;
  error: string | null;
}

// Common event names
export const USER_EVENTS = {
  ONBOARDING_STARTED: 'onboarding_started',
  ONBOARDING_COMPLETED: 'onboarding_completed',
  FEATURE_X_CLICKED: 'feature_x_clicked',
  GUEST_MODE_STARTED: 'guest_mode_started',
  ACCOUNT_CREATED: 'account_created',
  SIGN_IN_SUCCESS: 'sign_in_success',
  SIGN_OUT: 'sign_out',
} as const;

export function useUserEvents(): UseUserEventsReturn {
  const { user, isAuthenticated } = useAuth();
  const [events, setEvents] = useState<UserEvent[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load user events when user changes
  useEffect(() => {
    if (isAuthenticated && user) {
      loadUserEvents();
    } else {
      setEvents([]);
    }
  }, [isAuthenticated, user]);

  const loadUserEvents = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      setError(null);

      const { data, error: fetchError } = await supabase
        .from('user_events')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (fetchError) {
        logger.error('Error loading user events:', fetchError);
        setError(fetchError.message);
        return;
      }

      setEvents(data || []);
    } catch (err) {
      logger.error('Error loading user events:', err);
      setError('Failed to load user events');
    } finally {
      setIsLoading(false);
    }
  };

  const logEvent = async (eventName: string): Promise<boolean> => {
    try {
      setError(null);

      if (isAuthenticated && user) {
        // Log to Supabase for authenticated users
        const { error: insertError } = await supabase
          .from('user_events')
          .insert({
            user_id: user.id,
            event_name: eventName,
          });

        if (insertError) {
          logger.error('Error logging event to Supabase:', insertError);
          setError(insertError.message);
          return false;
        }

        // Add to local state
        const newEvent: UserEvent = {
          id: `temp-${Date.now()}`,
          user_id: user.id,
          event_name: eventName,
          created_at: new Date().toISOString(),
        };
        setEvents(prev => [newEvent, ...prev]);

        logger.info('Event logged to Supabase:', { eventName, userId: user.id });
        return true;
      } else {
        // For guests, store locally in AsyncStorage
        const guestEvents = await getGuestEvents();
        const newEvent = {
          id: `guest-${Date.now()}`,
          user_id: 'guest',
          event_name: eventName,
          created_at: new Date().toISOString(),
        };

        const updatedEvents = [newEvent, ...guestEvents];
        await AsyncStorage.setItem('guest_events', JSON.stringify(updatedEvents));

        logger.info('Event logged locally for guest:', { eventName });
        return true;
      }
    } catch (err) {
      logger.error('Error logging event:', err);
      setError('Failed to log event');
      return false;
    }
  };

  const hasEvent = (eventName: string): boolean => {
    if (isAuthenticated && user) {
      return events.some(event => event.event_name === eventName);
    } else {
      // For guests, check local storage
      return false; // We'll implement this with a separate function
    }
  };

  const getUserEvents = (): UserEvent[] => {
    return events;
  };

  return {
    logEvent,
    hasEvent,
    getUserEvents,
    isLoading,
    error,
  };
}

// Helper functions for guest mode
export async function getGuestEvents(): Promise<UserEvent[]> {
  try {
    const eventsJson = await AsyncStorage.getItem('guest_events');
    return eventsJson ? JSON.parse(eventsJson) : [];
  } catch (error) {
    logger.error('Error getting guest events:', error);
    return [];
  }
}

export async function hasGuestEvent(eventName: string): Promise<boolean> {
  try {
    const events = await getGuestEvents();
    return events.some(event => event.event_name === eventName);
  } catch (error) {
    logger.error('Error checking guest event:', error);
    return false;
  }
}

export async function migrateGuestEventsToUser(userId: string): Promise<boolean> {
  try {
    const guestEvents = await getGuestEvents();
    
    if (guestEvents.length === 0) {
      return true; // No events to migrate
    }

    // Insert all guest events for the user
    const eventsToInsert = guestEvents.map(event => ({
      user_id: userId,
      event_name: event.event_name,
      created_at: event.created_at,
    }));

    const { error } = await supabase
      .from('user_events')
      .insert(eventsToInsert);

    if (error) {
      logger.error('Error migrating guest events:', error);
      return false;
    }

    // Clear guest events from local storage
    await AsyncStorage.removeItem('guest_events');

    logger.info('Successfully migrated guest events to user:', { 
      userId, 
      eventCount: guestEvents.length 
    });

    return true;
  } catch (error) {
    logger.error('Error migrating guest events:', error);
    return false;
  }
}

export async function clearGuestEvents(): Promise<void> {
  try {
    await AsyncStorage.removeItem('guest_events');
    logger.info('Guest events cleared');
  } catch (error) {
    logger.error('Error clearing guest events:', error);
  }
}
