{"expo": {"name": "Everlasting Us", "slug": "everlasting-us", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "everlasting-us", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/splash.png", "resizeMode": "contain", "backgroundColor": "#F8BBD9"}, "assetBundlePatterns": ["**/*"], "description": "A secure relationship-building app for couples to strengthen their bond through guided activities and meaningful conversations.", "keywords": ["relationship", "couples", "love", "marriage", "dating", "communication", "activities", "therapy", "wellness", "mental health"], "category": "lifestyle", "primaryColor": "#F8BBD9", "backgroundColor": "#FFFFFF", "ios": {"supportsTablet": true, "bundleIdentifier": "com.everlasting.us", "buildNumber": "1", "appStoreUrl": "https://apps.apple.com/app/everlasting-us/id1234567890", "config": {"usesNonExemptEncryption": false}, "infoPlist": {"NSAppTransportSecurity": {"NSAllowsArbitraryLoads": false, "NSExceptionDomains": {"localhost": {"NSExceptionAllowsInsecureHTTPLoads": true}}}, "NSCameraUsageDescription": "This app uses the camera to capture relationship memories and moments.", "NSPhotoLibraryUsageDescription": "This app accesses your photo library to save relationship memories and moments.", "CFBundleDisplayName": "Everlasting Us", "CFBundleName": "Everlasting Us", "CFBundleShortVersionString": "1.0.0", "CFBundleVersion": "1", "LSRequiresIPhoneOS": true, "UILaunchStoryboardName": "SplashScreen", "UIRequiredDeviceCapabilities": ["armv7"], "UISupportedInterfaceOrientations": ["UIInterfaceOrientationPortrait"], "UISupportedInterfaceOrientations~ipad": ["UIInterfaceOrientationPortrait", "UIInterfaceOrientationPortraitUpsideDown", "UIInterfaceOrientationLandscapeLeft", "UIInterfaceOrientationLandscapeRight"]}}, "android": {"package": "com.everlasting.us", "versionCode": 1, "playStoreUrl": "https://play.google.com/store/apps/details?id=com.everlasting.us", "permissions": ["CAMERA", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE"], "usesCleartextTraffic": false, "adaptiveIcon": {"foregroundImage": "./assets/images/icon-foreground.png", "backgroundColor": "#F8BBD9"}, "intentFilters": [{"action": "VIEW", "autoVerify": true, "data": [{"scheme": "https", "host": "everlasting.us"}], "category": ["BROWSABLE", "DEFAULT"]}]}, "web": {"bundler": "metro", "output": "single", "favicon": "./assets/images/favicon.png", "headers": {"X-Content-Type-Options": "nosniff", "X-Frame-Options": "DENY", "X-XSS-Protection": "1; mode=block", "Referrer-Policy": "strict-origin-when-cross-origin"}}, "plugins": ["expo-router", "expo-font", "expo-web-browser", "expo-secure-store", "expo-camera"], "experiments": {"typedRoutes": true}, "privacy": "unlisted", "extra": {"eas": {"projectId": "your-project-id-here"}}, "owner": "everlasting-us", "updates": {"enabled": true, "checkAutomatically": "ON_LOAD", "fallbackToCacheTimeout": 0}, "runtimeVersion": {"policy": "sdkVersion"}}}