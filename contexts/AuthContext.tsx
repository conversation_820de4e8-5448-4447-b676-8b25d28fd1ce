import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { authService } from '../services/authService';
import { hybridStorageService } from '../services/hybridStorageService';
import { logger } from '../utils/logger';
import { migrateGuestEventsToUser, clearGuestEvents } from '../hooks/useUserEvents';

interface AuthContextType {
  user: any | null;
  profile: any | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  isGuest: boolean;
  isInitialized: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (data: any) => Promise<void>;
  signOut: () => Promise<void>;
  continueAsGuest: () => void;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<any | null>(null);
  const [profile, setProfile] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isGuest, setIsGuest] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    initializeAuth();
    
    // Listen for auth state changes
    const { data: { subscription } } = authService.onAuthStateChange(async (event, session) => {
      logger.info('Auth state changed:', { event, session: !!session });
      
      if (event === 'SIGNED_IN' && session) {
        setUser(session.user);
        await loadUserProfile(session.user.id);
        
        // Migrate guest events to user
        await migrateGuestEventsToUser(session.user.id);
        
        // Sync local data to cloud
        await hybridStorageService.syncAllLocalData();
      } else if (event === 'SIGNED_OUT') {
        setUser(null);
        setProfile(null);
        
        // Clear guest events
        await clearGuestEvents();
        
        // Clear local data
        await hybridStorageService.clearAllLocalData();
      }
    });

    return () => {
      subscription?.unsubscribe();
    };
  }, []);

  const initializeAuth = async () => {
    try {
      setIsLoading(true);
      const authData = await authService.getCurrentUser();
      
      if (authData) {
        setUser(authData.user);
        setProfile(authData.profile);
        logger.info('User authenticated on app start');
      } else {
        logger.info('No authenticated user found');
      }
    } catch (error) {
      logger.error('Error initializing auth:', error);
    } finally {
      setIsLoading(false);
      setIsInitialized(true);
    }
  };

  const loadUserProfile = async (userId: string) => {
    try {
      const userProfile = await authService.getUserProfile(userId);
      setProfile(userProfile);
    } catch (error) {
      logger.error('Error loading user profile:', error);
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      setIsLoading(true);
      const authData = await authService.signIn({ email, password });
      setUser(authData.user);
      setProfile(authData.profile);
      
      // Migrate guest events to user
      await migrateGuestEventsToUser(authData.user.id);
      
      // Sync any local data to cloud
      await hybridStorageService.syncAllLocalData();
    } catch (error) {
      logger.error('Sign in error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const signUp = async (data: any) => {
    try {
      setIsLoading(true);
      const authData = await authService.signUp(data);
      setUser(authData.user);
      setProfile(authData.profile);
    } catch (error) {
      logger.error('Sign up error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setIsLoading(true);
      await authService.signOut();
      setUser(null);
      setProfile(null);
      
      // Clear guest events
      await clearGuestEvents();
      
      // Clear local data
      await hybridStorageService.clearAllLocalData();
    } catch (error) {
      logger.error('Sign out error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const refreshUser = async () => {
    try {
      const authData = await authService.getCurrentUser();
      if (authData) {
        setUser(authData.user);
        setProfile(authData.profile);
      }
    } catch (error) {
      logger.error('Error refreshing user:', error);
    }
  };

  const continueAsGuest = () => {
    setIsGuest(true);
    setUser(null);
    setProfile(null);
    logger.info('User continued as guest');
  };

  // Ensure we always have a valid context value
  const value: AuthContextType = {
    user,
    profile,
    isLoading,
    isAuthenticated: !!user && !isGuest,
    isGuest,
    isInitialized,
    signIn,
    signUp,
    signOut,
    continueAsGuest,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
