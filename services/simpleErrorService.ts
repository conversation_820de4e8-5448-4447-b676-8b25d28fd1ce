import { logger } from '../utils/logger';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Alert, Share } from 'react-native';

export interface SimpleErrorReport {
  id: string;
  timestamp: string;
  error: string;
  stack?: string;
  component?: string;
  action?: string;
  userId?: string;
  metadata?: Record<string, any>;
}

class SimpleErrorService {
  private errorQueue: SimpleErrorReport[] = [];
  private readonly STORAGE_KEY = 'simple_error_reports';

  constructor() {
    this.loadErrorQueue();
  }

  /**
   * Report an error with context
   */
  async reportError(
    error: Error | string,
    context: {
      component?: string;
      action?: string;
      userId?: string;
      metadata?: Record<string, any>;
    } = {}
  ): Promise<void> {
    try {
      const errorReport: SimpleErrorReport = {
        id: this.generateId(),
        timestamp: new Date().toISOString(),
        error: typeof error === 'string' ? error : error.message,
        stack: typeof error === 'string' ? undefined : error.stack,
        component: context.component,
        action: context.action,
        userId: context.userId,
        metadata: context.metadata,
      };

      // Add to queue
      this.errorQueue.push(errorReport);
      
      // Keep only last 20 errors
      if (this.errorQueue.length > 20) {
        this.errorQueue = this.errorQueue.slice(-20);
      }

      // Save to storage
      await this.saveErrorQueue();

      // Log locally
      logger.error('Error reported:', errorReport);

      // Show error to user if it's a critical error
      if (context.action === 'CriticalError') {
        this.showErrorToUser(errorReport);
      }

    } catch (err) {
      logger.error('Failed to report error:', err);
    }
  }

  /**
   * Show error to user and offer to share
   */
  private showErrorToUser(errorReport: SimpleErrorReport): void {
    Alert.alert(
      'Something went wrong',
      'An error occurred. Would you like to share the error details to help us fix it?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Share Error',
          onPress: () => this.shareErrorReport(errorReport),
        },
      ]
    );
  }

  /**
   * Share error report
   */
  private async shareErrorReport(errorReport: SimpleErrorReport): Promise<void> {
    try {
      const errorText = `
Error Report - Everlasting Us App

Error ID: ${errorReport.id}
Timestamp: ${errorReport.timestamp}
Component: ${errorReport.component || 'Unknown'}
Action: ${errorReport.action || 'Unknown'}
User ID: ${errorReport.userId || 'Anonymous'}

Error Message:
${errorReport.error}

${errorReport.stack ? `Stack Trace:\n${errorReport.stack}` : ''}

${errorReport.metadata ? `Additional Info:\n${JSON.stringify(errorReport.metadata, null, 2)}` : ''}

Please send this to: <EMAIL>
      `.trim();

      await Share.share({
        message: errorText,
        title: 'Error Report - Everlasting Us',
      });
    } catch (error) {
      logger.error('Failed to share error report:', error);
    }
  }

  /**
   * Get all error reports as text
   */
  async getAllErrorsAsText(): Promise<string> {
    const errors = this.errorQueue.map(error => `
=== Error Report ===
ID: ${error.id}
Timestamp: ${error.timestamp}
Component: ${error.component || 'Unknown'}
Action: ${error.action || 'Unknown'}
User ID: ${error.userId || 'Anonymous'}

Error: ${error.error}

${error.stack ? `Stack:\n${error.stack}` : ''}

${error.metadata ? `Metadata:\n${JSON.stringify(error.metadata, null, 2)}` : ''}
    `).join('\n');

    return `Everlasting Us - Error Reports\nGenerated: ${new Date().toISOString()}\n\n${errors}`;
  }

  /**
   * Share all error reports
   */
  async shareAllErrors(): Promise<void> {
    try {
      const errorText = await this.getAllErrorsAsText();
      
      await Share.share({
        message: errorText,
        title: 'All Error Reports - Everlasting Us',
      });
    } catch (error) {
      logger.error('Failed to share all errors:', error);
    }
  }

  /**
   * Get error statistics
   */
  getErrorStats(): {
    totalErrors: number;
    recentErrors: SimpleErrorReport[];
    errorTypes: Record<string, number>;
  } {
    const recentErrors = this.errorQueue.slice(-5);
    const errorTypes: Record<string, number> = {};

    this.errorQueue.forEach(error => {
      const type = error.component || 'Unknown';
      errorTypes[type] = (errorTypes[type] || 0) + 1;
    });

    return {
      totalErrors: this.errorQueue.length,
      recentErrors,
      errorTypes,
    };
  }

  /**
   * Clear all error reports
   */
  async clearErrorReports(): Promise<void> {
    this.errorQueue = [];
    await AsyncStorage.removeItem(this.STORAGE_KEY);
    logger.info('All error reports cleared');
  }

  /**
   * Load error queue from storage
   */
  private async loadErrorQueue(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        this.errorQueue = JSON.parse(stored);
      }
    } catch (error) {
      logger.error('Failed to load error queue:', error);
      this.errorQueue = [];
    }
  }

  /**
   * Save error queue to storage
   */
  private async saveErrorQueue(): Promise<void> {
    try {
      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.errorQueue));
    } catch (error) {
      logger.error('Failed to save error queue:', error);
    }
  }

  /**
   * Generate unique ID
   */
  private generateId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

export const simpleErrorService = new SimpleErrorService();

// Global error handler
export const setupGlobalErrorHandling = () => {
  // Handle unhandled promise rejections
  const originalHandler = global.ErrorUtils?.getGlobalHandler();
  
  global.ErrorUtils?.setGlobalHandler((error, isFatal) => {
    simpleErrorService.reportError(error, {
      component: 'Global',
      action: isFatal ? 'UnhandledFatalError' : 'UnhandledError',
    });
    
    if (originalHandler) {
      originalHandler(error, isFatal);
    }
  });

  // Handle unhandled promise rejections
  if (typeof window !== 'undefined') {
    window.addEventListener('unhandledrejection', (event) => {
      simpleErrorService.reportError(
        new Error(`Unhandled Promise Rejection: ${event.reason}`),
        {
          component: 'Global',
          action: 'UnhandledPromiseRejection',
          metadata: { reason: event.reason },
        }
      );
    });
  }
};
