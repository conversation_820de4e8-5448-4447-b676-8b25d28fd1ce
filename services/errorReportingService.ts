import { logger } from '../utils/logger';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface ErrorReport {
  id: string;
  timestamp: string;
  error: string;
  stack?: string;
  userId?: string;
  userAgent?: string;
  url?: string;
  component?: string;
  action?: string;
  metadata?: Record<string, any>;
}

class ErrorReportingService {
  private errorQueue: ErrorReport[] = [];
  private readonly MAX_QUEUE_SIZE = 50;
  private readonly STORAGE_KEY = 'error_reports_queue';
  private readonly REPORTING_ENDPOINT = 'https://api.emailjs.com/api/v1.0/email/send';

  constructor() {
    this.loadErrorQueue();
  }

  /**
   * Report an error with context
   */
  async reportError(
    error: Error | string,
    context: {
      component?: string;
      action?: string;
      userId?: string;
      metadata?: Record<string, any>;
    } = {}
  ): Promise<void> {
    try {
      const errorReport: ErrorReport = {
        id: this.generateId(),
        timestamp: new Date().toISOString(),
        error: typeof error === 'string' ? error : error.message,
        stack: typeof error === 'string' ? undefined : error.stack,
        userId: context.userId,
        userAgent: this.getUserAgent(),
        url: this.getCurrentUrl(),
        component: context.component,
        action: context.action,
        metadata: context.metadata,
      };

      // Add to queue
      this.errorQueue.push(errorReport);
      
      // Trim queue if too large
      if (this.errorQueue.length > this.MAX_QUEUE_SIZE) {
        this.errorQueue = this.errorQueue.slice(-this.MAX_QUEUE_SIZE);
      }

      // Save to storage
      await this.saveErrorQueue();

      // Log locally
      logger.error('Error reported:', errorReport);

      // Try to send immediately (non-blocking)
      this.sendErrorReport(errorReport).catch(err => {
        logger.error('Failed to send error report:', err);
      });

    } catch (err) {
      logger.error('Failed to report error:', err);
    }
  }

  /**
   * Send error report via email
   */
  private async sendErrorReport(errorReport: ErrorReport): Promise<void> {
    try {
      const emailData = {
        service_id: 'your_emailjs_service_id', // You'll need to set this up
        template_id: 'your_emailjs_template_id', // You'll need to set this up
        user_id: 'your_emailjs_user_id', // You'll need to set this up
        template_params: {
          to_email: '<EMAIL>', // Replace with your email
          error_id: errorReport.id,
          error_message: errorReport.error,
          error_stack: errorReport.stack || 'No stack trace',
          timestamp: errorReport.timestamp,
          user_id: errorReport.userId || 'Anonymous',
          component: errorReport.component || 'Unknown',
          action: errorReport.action || 'Unknown',
          user_agent: errorReport.userAgent || 'Unknown',
          url: errorReport.url || 'Unknown',
          metadata: JSON.stringify(errorReport.metadata || {}, null, 2),
        },
      };

      const response = await fetch(this.REPORTING_ENDPOINT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(emailData),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      logger.info('Error report sent successfully:', errorReport.id);

    } catch (error) {
      logger.error('Failed to send error report:', error);
      throw error;
    }
  }

  /**
   * Send all queued errors
   */
  async sendQueuedErrors(): Promise<void> {
    const errorsToSend = [...this.errorQueue];
    
    for (const errorReport of errorsToSend) {
      try {
        await this.sendErrorReport(errorReport);
        // Remove from queue after successful send
        this.errorQueue = this.errorQueue.filter(e => e.id !== errorReport.id);
      } catch (error) {
        logger.error('Failed to send queued error:', error);
        // Keep in queue for retry later
      }
    }

    await this.saveErrorQueue();
  }

  /**
   * Get error statistics
   */
  getErrorStats(): {
    totalErrors: number;
    recentErrors: ErrorReport[];
    errorTypes: Record<string, number>;
  } {
    const recentErrors = this.errorQueue.slice(-10);
    const errorTypes: Record<string, number> = {};

    this.errorQueue.forEach(error => {
      const type = error.component || 'Unknown';
      errorTypes[type] = (errorTypes[type] || 0) + 1;
    });

    return {
      totalErrors: this.errorQueue.length,
      recentErrors,
      errorTypes,
    };
  }

  /**
   * Clear all error reports
   */
  async clearErrorReports(): Promise<void> {
    this.errorQueue = [];
    await AsyncStorage.removeItem(this.STORAGE_KEY);
    logger.info('All error reports cleared');
  }

  /**
   * Load error queue from storage
   */
  private async loadErrorQueue(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        this.errorQueue = JSON.parse(stored);
      }
    } catch (error) {
      logger.error('Failed to load error queue:', error);
      this.errorQueue = [];
    }
  }

  /**
   * Save error queue to storage
   */
  private async saveErrorQueue(): Promise<void> {
    try {
      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.errorQueue));
    } catch (error) {
      logger.error('Failed to save error queue:', error);
    }
  }

  /**
   * Generate unique ID
   */
  private generateId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get user agent string
   */
  private getUserAgent(): string {
    if (typeof navigator !== 'undefined') {
      return navigator.userAgent;
    }
    return 'React Native App';
  }

  /**
   * Get current URL
   */
  private getCurrentUrl(): string {
    if (typeof window !== 'undefined') {
      return window.location.href;
    }
    return 'React Native App';
  }
}

export const errorReportingService = new ErrorReportingService();

// Global error handler
export const setupGlobalErrorHandling = () => {
  // Handle unhandled promise rejections
  const originalHandler = global.ErrorUtils?.getGlobalHandler();
  
  global.ErrorUtils?.setGlobalHandler((error, isFatal) => {
    errorReportingService.reportError(error, {
      component: 'Global',
      action: isFatal ? 'UnhandledFatalError' : 'UnhandledError',
    });
    
    if (originalHandler) {
      originalHandler(error, isFatal);
    }
  });

  // Handle unhandled promise rejections
  if (typeof window !== 'undefined') {
    window.addEventListener('unhandledrejection', (event) => {
      errorReportingService.reportError(
        new Error(`Unhandled Promise Rejection: ${event.reason}`),
        {
          component: 'Global',
          action: 'UnhandledPromiseRejection',
          metadata: { reason: event.reason },
        }
      );
    });
  }
};
