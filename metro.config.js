const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Configure Metro to handle source maps better
config.resolver.platforms = ['ios', 'android', 'native', 'web'];

// Improve source map handling
config.transformer.minifierConfig = {
  keep_fnames: true,
  mangle: {
    keep_fnames: true,
  },
};

// Better error handling for symbolication
config.symbolicator = {
  customizeFrame: (frame) => {
    // Handle anonymous frames better
    if (frame.file === '<anonymous>' || !frame.file) {
      return {
        ...frame,
        file: 'Unknown',
        line: frame.line || 0,
        column: frame.column || 0,
      };
    }
    return frame;
  },
};

module.exports = config;
